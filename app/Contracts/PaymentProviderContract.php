<?php

namespace App\Contracts;

use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;

interface PaymentProviderContract
{
    /**
     * Tworzy nową subskrypcję u dostawcy.
     */
    public function createSubscription(User $user, string $planId, array $options = []): mixed;

    /**
     * Anuluje aktywną subskrypcję u dostawcy.
     */
    public function cancelSubscription(Subscription $subscription): bool;

    /**
     * Pobiera status subskrypcji zewnętrznej.
     */
    public function getSubscriptionStatus(Subscription $subscription): string;

    /**
     * Tworzy jednorazową płatno<PERSON>ć (np. dla zakupu planu bez subskrypcji).
     */
    public function charge(User $user, float $amount, string $currency = 'PLN', array $options = []): mixed;

    /**
     * Obsługuje webhook z płatnościami (np. Stripe Webhook).
     */
    public function handleWebhook(Request $request): void;

    /**
     * Zwraca nazwę providera, np. "stripe", "payu".
     */
    public function getName(): string;
}
