<?php

namespace App\Filament\Resources;

use App\Enums\PlansPeriod;
use App\Enums\SystemModules;
use App\Filament\Resources\PlanResource\Pages;
use App\Filament\Resources\PlanResource\RelationManagers;
use App\Models\Plan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('price')
                    ->required()
                    ->numeric()
                    ->prefix('$'),
                Forms\Components\Select::make('period')
                    ->options(PlansPeriod::class)
                    ->default(PlansPeriod::YEARLY->value)
                    ->required(),
                Forms\Components\CheckboxList::make('features')
                    ->options(SystemModules::enabledToArrayWithLabels())
                    ->columns(2)
                    ->columnSpanFull()
                    ->label('Features'),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->money('PLN', 0, 'pl_PL')
                    ->sortable(),
                Tables\Columns\TextColumn::make('period')
                    ->formatStateUsing(fn($state) => $state?->label() ?? 'Unknown')
                    ->searchable(),
                Tables\Columns\TextColumn::make('features')
                    ->formatStateUsing(function ($state, Plan $record) {
                        if (empty($state)) {
                            return 'No features selected';
                        }
                        $labels = [];
                        foreach ($record->features as $moduleValue) {
                            $module = SystemModules::tryFrom($moduleValue);
                            if ($module) {
                                $labels[] = $module->label();
                            }
                        }

                        return implode(', ', $labels);
                    })
                    ->wrap()
                    ->searchable(false)
                    ->sortable(false),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }
}
