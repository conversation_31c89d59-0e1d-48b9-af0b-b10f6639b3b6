<?php

namespace App\Services\Payments;

use App\Contracts\PaymentProviderContract;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;

class PayUPaymentProvider implements PaymentProviderContract
{
    public function createSubscription(User $user, string $planId, array $options = []): mixed
    {
        // Implementacja tworzenia subskrypcji w PayU
        // Wysyłanie żądania do PayU REST API
    }

    public function cancelSubscription(Subscription $subscription): bool
    {
        // PayU nie wspiera subskrypcji natywnie - możesz anulować z poziomu aplikacji
        return true;
    }

    public function getSubscriptionStatus(Subscription $subscription): string
    {
        // Zwraca lokalny status lub wykonuje zapytanie do API (jeśli obsługujesz recurring przez PayU)
        return $subscription->status;
    }

    public function charge(User $user, float $amount, string $currency = 'PLN', array $options = []): mixed
    {
        // Tworzy jednorazową płat<PERSON><PERSON> przez PayU
    }

    public function handleWebhook(Request $request): void
    {
        // Parsowanie webhooka od PayU (np. payment status changed)
    }

    public function getName(): string
    {
        return 'payu';
    }
}
